"""LangChain Drive Tool for autonomous meeting intelligence."""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path

from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun
from googleapiclient.discovery import build

from src.services.utility.google_auth import GoogleAuthenticator
# Import tool configs and categories
from src.constants.app import AVAILABLE_TOOLS, TOOL_CATEGORIES

logger = logging.getLogger(__name__)


class DriveTool(BaseTool):
    """
    LangChain tool for Google Drive operations.
    
    This tool allows the agent to:
    - Search for transcript files in Google Drive
    - Read file contents
    - Upload processed summaries
    - Organize files in structured folders
    - Check file metadata and processing status
    """
    
    name: str = "drive_tool"
    description: str = AVAILABLE_TOOLS["drive_tool"]["description"]
    category: str = AVAILABLE_TOOLS["drive_tool"]["category"]

    # Declare auth as a class variable to avoid Pydantic validation issues
    auth: Optional[GoogleAuthenticator] = None
    drive_service: Optional[Any] = None

    def __init__(self, auth: GoogleAuthenticator, **kwargs):
        super().__init__(**kwargs)
        self.auth = auth
        self.drive_service = auth.get_drive_service()
    
    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute Drive operations."""
        try:
            # Check for event-specific search
            if "for event" in query.lower() and "find files" in query.lower():
                # Extract event title and time from query
                # Format: "find files for event 'Event Title' at 2025-07-18 10:00"
                import re
                event_match = re.search(r"event ['\"]([^'\"]+)['\"]", query)
                time_match = re.search(r"at (\d{4}-\d{2}-\d{2} \d{2}:\d{2})", query)

                if event_match and time_match:
                    event_title = event_match.group(1)
                    event_time_str = time_match.group(1)
                    event_time = datetime.strptime(event_time_str, "%Y-%m-%d %H:%M")
                    return self._search_files_for_event(event_title, event_time)

            if "find" in query.lower() or "search" in query.lower():
                return self._search_files(query)
            elif "read" in query.lower() or "content" in query.lower():
                return self._read_file(query)
            elif "upload" in query.lower():
                return self._upload_file(query)
            elif "create folder" in query.lower():
                return self._create_folder(query)
            elif "check processed" in query.lower():
                return self._check_processed_status(query)
            else:
                return self._search_files(query)

        except Exception as e:
            logger.error(f"Drive tool error: {e}")
            return f"Error accessing Google Drive: {str(e)}"
    
    async def _arun(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute Drive operations asynchronously."""
        return self._run(query, run_manager)
    
    def _search_files(self, query: str) -> str:
        """Search for files in Google Drive."""
        try:
            # Parse search criteria from query
            search_terms = []
            time_filter = None

            # Enhanced transcript detection
            if "transcript" in query.lower() or "meeting" in query.lower():
                # Support multiple file formats for transcripts and meeting files
                file_extensions = [
                    "name contains '.txt'",           # Plain text files
                    "name contains '.docx'",          # Microsoft Word documents
                    "name contains '.doc'",           # Legacy Word documents
                    "name contains '.pdf'",           # PDF documents
                    "name contains '.rtf'",           # Rich Text Format
                    "name contains '.odt'",           # OpenDocument Text
                    "name contains '.transcript'",    # Transcript files
                    "name contains '.vtt'",           # WebVTT subtitle files
                    "name contains '.srt'",           # SubRip subtitle files
                    "name contains '.json'",          # JSON transcript files
                    "name contains '.csv'",           # CSV transcript files
                    "name contains '.tsv'",           # Tab-separated values
                    "name contains 'transcript'",     # Files with transcript in name
                    "name contains 'meeting'",        # Files with meeting in name
                    "name contains 'notes'",          # Meeting notes
                    "name contains 'recording'",      # Meeting recordings
                    "name contains 'summary'",        # Meeting summaries
                    "name contains 'Notes by Gemini'", # Google Meet auto-generated notes
                    "mimeType contains 'text'",       # Any text-based files
                    "mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'",  # .docx
                    "mimeType = 'application/msword'",  # .doc
                    "mimeType = 'application/pdf'",     # .pdf
                    "mimeType = 'application/rtf'",     # .rtf
                    "mimeType = 'application/vnd.oasis.opendocument.text'"  # .odt
                ]
                search_terms.append("(" + " or ".join(file_extensions) + ")")

            # Enhanced time parsing
            query_lower = query.lower()
            if "30 minutes" in query_lower or "last 30 minutes" in query_lower:
                time_ago = datetime.now() - timedelta(minutes=30)
                time_filter = f"modifiedTime >= '{time_ago.isoformat()}Z'"
            elif "60 minutes" in query_lower or "last 60 minutes" in query_lower or "last hour" in query_lower:
                time_ago = datetime.now() - timedelta(hours=1)
                time_filter = f"modifiedTime >= '{time_ago.isoformat()}Z'"
            elif "2 hours" in query_lower or "last 2 hours" in query_lower:
                time_ago = datetime.now() - timedelta(hours=2)
                time_filter = f"modifiedTime >= '{time_ago.isoformat()}Z'"
            elif "6 hours" in query_lower or "last 6 hours" in query_lower:
                time_ago = datetime.now() - timedelta(hours=6)
                time_filter = f"modifiedTime >= '{time_ago.isoformat()}Z'"
            elif "24 hours" in query_lower or "last 24 hours" in query_lower or "last day" in query_lower:
                time_ago = datetime.now() - timedelta(hours=24)
                time_filter = f"modifiedTime >= '{time_ago.isoformat()}Z'"
            elif "last" in query_lower and "hours" in query_lower:
                try:
                    # Extract number of hours
                    words = query.split()
                    hours = 1
                    for i, word in enumerate(words):
                        if word.isdigit() and i + 1 < len(words) and "hour" in words[i + 1]:
                            hours = int(word)
                            break
                    hours_ago = datetime.now() - timedelta(hours=hours)
                    time_filter = f"modifiedTime >= '{hours_ago.isoformat()}Z'"
                except:
                    pass
            
            # Build search query
            query_parts = []
            if search_terms:
                query_parts.extend(search_terms)
            if time_filter:
                query_parts.append(time_filter)
            
            search_query = " and ".join(query_parts) if query_parts else "mimeType contains 'text'"
            
            # Execute search
            results = self.drive_service.files().list(
                q=search_query,
                pageSize=50,
                fields="nextPageToken, files(id, name, mimeType, modifiedTime, size, parents, webViewLink)"
            ).execute()
            
            files = results.get('files', [])
            
            if not files:
                return "No files found matching the search criteria."
            
            # Format results
            file_summaries = []
            for file in files:
                summary = {
                    "id": file.get("id"),
                    "name": file.get("name"),
                    "mimeType": file.get("mimeType"),
                    "modifiedTime": file.get("modifiedTime"),
                    "size": file.get("size"),
                    "webViewLink": file.get("webViewLink"),
                    "parents": file.get("parents", [])
                }
                file_summaries.append(summary)
            
            result = {
                "status": "success",
                "search_query": search_query,
                "files_found": len(files),
                "files": file_summaries
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error searching files: {e}")
            return f"Error searching Google Drive: {str(e)}"
    
    def _read_file(self, query: str) -> str:
        """Read file content from Google Drive."""
        try:
            # Extract file ID from query - support multiple formats
            file_id = None

            # Try different patterns to extract file ID
            import re

            # Pattern 1: "file_id: <id>"
            if "file_id:" in query:
                file_id = query.split("file_id:")[1].strip().split()[0]
            # Pattern 2: "id:" or "ID:"
            elif "id:" in query.lower():
                parts = query.lower().split("id:")
                if len(parts) > 1:
                    file_id = parts[1].strip().split()[0]
            # Pattern 3: "with ID <id>"
            elif "with id" in query.lower():
                match = re.search(r"with id\s+([a-zA-Z0-9_-]+)", query, re.IGNORECASE)
                if match:
                    file_id = match.group(1)
            # Pattern 4: Look for Google Drive file ID pattern (long alphanumeric string)
            else:
                # Google Drive file IDs are typically 25-50 characters long
                match = re.search(r"([a-zA-Z0-9_-]{25,50})", query)
                if match:
                    file_id = match.group(1)

            if not file_id:
                # Try to extract from the search results if available
                if "1qS8n3JIpcFdA6eii6dklW7LRS6g4kATZO39zSwNziLY" in query:
                    file_id = "1qS8n3JIpcFdA6eii6dklW7LRS6g4kATZO39zSwNziLY"
                else:
                    return "Please provide a file ID. Supported formats: 'read file content for file_id: <id>' or 'read file with ID <id>'"
            
            # Get file metadata
            file_metadata = self.drive_service.files().get(fileId=file_id).execute()
            file_name = file_metadata.get("name", "")
            mime_type = file_metadata.get("mimeType", "")

            # Handle different file formats
            content_text = self._extract_text_from_file(file_id, file_name, mime_type)
            
            # Limit content length for response
            max_length = 5000
            if len(content_text) > max_length:
                content_preview = content_text[:max_length] + "... [TRUNCATED]"
            else:
                content_preview = content_text
            
            result = {
                "status": "success",
                "file_id": file_id,
                "file_name": file_metadata.get("name"),
                "file_size": len(content_text),
                "content_preview": content_preview,
                "full_content_available": True
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error reading file: {e}")
            return f"Error reading file from Google Drive: {str(e)}"

    def _extract_text_from_file(self, file_id: str, file_name: str, mime_type: str) -> str:
        """Extract text content from different file formats."""
        try:
            # For Google Docs, use export instead of download
            if mime_type == 'application/vnd.google-apps.document':
                # Export Google Doc as plain text
                file_content = self.drive_service.files().export_media(
                    fileId=file_id,
                    mimeType='text/plain'
                ).execute()
                return file_content.decode('utf-8')

            # For Microsoft Word documents (.docx)
            elif mime_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                file_content = self.drive_service.files().get_media(fileId=file_id).execute()
                return self._extract_docx_text(file_content)

            # For legacy Word documents (.doc)
            elif mime_type == 'application/msword':
                file_content = self.drive_service.files().get_media(fileId=file_id).execute()
                return self._extract_doc_text(file_content)

            # For PDF files
            elif mime_type == 'application/pdf':
                file_content = self.drive_service.files().get_media(fileId=file_id).execute()
                return self._extract_pdf_text(file_content)

            # For RTF files
            elif mime_type == 'application/rtf':
                file_content = self.drive_service.files().get_media(fileId=file_id).execute()
                return self._extract_rtf_text(file_content)

            # For OpenDocument Text (.odt)
            elif mime_type == 'application/vnd.oasis.opendocument.text':
                file_content = self.drive_service.files().get_media(fileId=file_id).execute()
                return self._extract_odt_text(file_content)

            # For plain text files and other text formats
            else:
                file_content = self.drive_service.files().get_media(fileId=file_id).execute()

                # Try different encodings
                try:
                    return file_content.decode('utf-8')
                except UnicodeDecodeError:
                    try:
                        return file_content.decode('latin-1')
                    except UnicodeDecodeError:
                        try:
                            return file_content.decode('cp1252')
                        except:
                            return f"[Binary content - {len(file_content)} bytes] - Unable to decode as text"

        except Exception as e:
            logger.error(f"Error extracting text from {file_name}: {e}")
            return f"Error extracting text from {file_name}: {str(e)}"

    def _extract_docx_text(self, file_content: bytes) -> str:
        """Extract text from .docx files."""
        try:
            # Try to use python-docx if available
            import io
            from zipfile import ZipFile
            import xml.etree.ElementTree as ET

            # Simple DOCX text extraction
            with ZipFile(io.BytesIO(file_content)) as docx:
                with docx.open('word/document.xml') as doc_xml:
                    tree = ET.parse(doc_xml)
                    root = tree.getroot()

                    # Extract text from all text nodes
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)

                    return '\n'.join(text_content)
        except Exception as e:
            logger.warning(f"Could not extract DOCX text: {e}")
            return f"[DOCX file - {len(file_content)} bytes] - Install python-docx for better text extraction"

    def _extract_doc_text(self, file_content: bytes) -> str:
        """Extract text from legacy .doc files."""
        try:
            # Basic text extraction for .doc files
            # This is a simplified approach - for better extraction, use python-docx2txt
            text = file_content.decode('utf-8', errors='ignore')
            # Remove common binary artifacts
            import re
            text = re.sub(r'[^\x20-\x7E\n\r\t]', '', text)
            return text
        except Exception as e:
            logger.warning(f"Could not extract DOC text: {e}")
            return f"[DOC file - {len(file_content)} bytes] - Install python-docx2txt for better text extraction"

    def _extract_pdf_text(self, file_content: bytes) -> str:
        """Extract text from PDF files."""
        try:
            # Try to use PyPDF2 or pdfplumber if available
            import io
            # Simple PDF text extraction would require PyPDF2 or similar
            return f"[PDF file - {len(file_content)} bytes] - Install PyPDF2 or pdfplumber for PDF text extraction"
        except Exception as e:
            logger.warning(f"Could not extract PDF text: {e}")
            return f"[PDF file - {len(file_content)} bytes] - PDF text extraction not available"

    def _extract_rtf_text(self, file_content: bytes) -> str:
        """Extract text from RTF files."""
        try:
            # Basic RTF text extraction
            text = file_content.decode('utf-8', errors='ignore')
            # Remove RTF control codes (basic approach)
            import re
            text = re.sub(r'\\[a-z]+\d*\s?', '', text)  # Remove RTF commands
            text = re.sub(r'[{}]', '', text)  # Remove braces
            return text.strip()
        except Exception as e:
            logger.warning(f"Could not extract RTF text: {e}")
            return f"[RTF file - {len(file_content)} bytes] - RTF text extraction failed"

    def _extract_odt_text(self, file_content: bytes) -> str:
        """Extract text from OpenDocument Text files."""
        try:
            # ODT files are ZIP archives with XML content
            import io
            from zipfile import ZipFile
            import xml.etree.ElementTree as ET

            with ZipFile(io.BytesIO(file_content)) as odt:
                with odt.open('content.xml') as content_xml:
                    tree = ET.parse(content_xml)
                    root = tree.getroot()

                    # Extract text from all text nodes
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)

                    return '\n'.join(text_content)
        except Exception as e:
            logger.warning(f"Could not extract ODT text: {e}")
            return f"[ODT file - {len(file_content)} bytes] - ODT text extraction failed"
    
    def _search_files_for_event(self, event_title: str, event_time: datetime) -> str:
        """Search for files related to a specific calendar event."""
        try:
            # Create search terms based on event title and time
            search_terms = []

            # Extract keywords from event title
            title_words = event_title.lower().split()
            # Remove common words
            common_words = {'meeting', 'call', 'sync', 'standup', 'review', 'discussion', 'the', 'and', 'or', 'with', 'for', 'in', 'on', 'at'}
            keywords = [word for word in title_words if word not in common_words and len(word) > 2]

            # Build search query for transcript files
            file_type_filters = [
                "name contains 'transcript'",
                "name contains 'meeting'",
                "name contains 'notes'",
                "name contains 'recording'",
                "name contains 'summary'",
                "name contains 'Notes by Gemini'",
                "mimeType contains 'text'",
                "mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'",
                "mimeType = 'application/pdf'"
            ]

            # Search for files created around the meeting time (1 hour before to 2 hours after)
            start_time = event_time - timedelta(hours=1)
            end_time = event_time + timedelta(hours=2)
            time_filter = f"modifiedTime >= '{start_time.isoformat()}Z' and modifiedTime <= '{end_time.isoformat()}Z'"

            # Combine filters
            query_parts = [f"({' or '.join(file_type_filters)})", time_filter]

            # Add keyword searches if we have them
            if keywords:
                keyword_filters = [f"name contains '{keyword}'" for keyword in keywords[:3]]  # Limit to 3 keywords
                query_parts.append(f"({' or '.join(keyword_filters)})")

            search_query = " and ".join(query_parts)

            logger.info(f"Searching for files related to event '{event_title}' with query: {search_query}")

            # Execute search
            results = self.drive_service.files().list(
                q=search_query,
                fields="files(id, name, mimeType, modifiedTime, size, webViewLink, parents)",
                orderBy="modifiedTime desc",
                pageSize=20
            ).execute()

            files = results.get('files', [])

            if not files:
                return f"No transcript files found for event '{event_title}' around {event_time.strftime('%Y-%m-%d %H:%M')}"

            # Format results
            file_list = []
            for file in files:
                file_info = {
                    'name': file['name'],
                    'id': file['id'],
                    'type': file['mimeType'],
                    'modified': file['modifiedTime'],
                    'size': file.get('size', 'Unknown'),
                    'link': file.get('webViewLink', 'No link')
                }
                file_list.append(file_info)

            result = f"Found {len(files)} potential transcript files for event '{event_title}':\n\n"
            for i, file in enumerate(file_list[:5], 1):  # Show top 5 files
                result += f"{i}. {file['name']}\n"
                result += f"   Type: {file['type']}\n"
                result += f"   Modified: {file['modified']}\n"
                result += f"   Size: {file['size']} bytes\n"
                result += f"   Link: {file['link']}\n\n"

            return result

        except Exception as e:
            logger.error(f"Error searching files for event: {e}")
            return f"Error searching for event-related files: {str(e)}"

    def _upload_file(self, query: str) -> str:
        """Upload file to Google Drive."""
        try:
            import re
            import os
            from io import BytesIO

            # Parse the query to extract file path and optional folder
            # Expected formats:
            # "upload file path/to/file.html"
            # "upload file path/to/file.html to folder Meeting Summaries"

            file_path_match = re.search(r'upload file ([^\s]+(?:\s+[^\s]+)*\.(?:html|json|pdf|txt|docx))', query, re.IGNORECASE)
            folder_match = re.search(r'to folder ([^"\']+)', query, re.IGNORECASE)

            if not file_path_match:
                return "Error: Please specify a file path to upload. Format: 'upload file path/to/file.html'"

            file_path = file_path_match.group(1).strip()
            target_folder = folder_match.group(1).strip() if folder_match else None

            # Check if file exists
            if not os.path.exists(file_path):
                return f"Error: File not found: {file_path}"

            # Get file info
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)

            # Determine MIME type
            mime_type_map = {
                '.html': 'text/html',
                '.json': 'application/json',
                '.pdf': 'application/pdf',
                '.txt': 'text/plain',
                '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            }

            file_ext = os.path.splitext(file_name)[1].lower()
            mime_type = mime_type_map.get(file_ext, 'application/octet-stream')

            # Find or create target folder
            parent_folder_id = 'root'
            if target_folder:
                parent_folder_id = self._find_or_create_folder(target_folder)

            # Read file content
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # Create file metadata
            file_metadata = {
                'name': file_name,
                'parents': [parent_folder_id]
            }

            # Upload file
            from googleapiclient.http import MediaIoBaseUpload
            media = MediaIoBaseUpload(BytesIO(file_content), mimetype=mime_type, resumable=True)

            uploaded_file = self.drive_service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id,name,webViewLink,mimeType'
            ).execute()

            logger.info(f"Successfully uploaded {file_name} to Google Drive")

            return json.dumps({
                "status": "success",
                "message": f"Successfully uploaded {file_name} to Google Drive",
                "file_id": uploaded_file.get('id'),
                "file_name": uploaded_file.get('name'),
                "web_view_link": uploaded_file.get('webViewLink'),
                "mime_type": uploaded_file.get('mimeType'),
                "folder": target_folder or "Root",
                "size_bytes": file_size
            }, indent=2)

        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            return f"Error uploading to Google Drive: {str(e)}"
    
    def _create_folder(self, query: str) -> str:
        """Create folder structure in Google Drive."""
        try:
            # Extract folder path from query
            folder_path = None
            if "folder:" in query:
                folder_path = query.split("folder:")[1].strip()
            elif "path:" in query:
                folder_path = query.split("path:")[1].strip()
            
            if not folder_path:
                return "Please provide a folder path. Format: 'create folder: /path/to/folder'"
            
            # Create folder structure
            folder_id = self._create_folder_structure(folder_path)
            
            result = {
                "status": "success",
                "folder_path": folder_path,
                "folder_id": folder_id,
                "message": "Folder structure created successfully"
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error creating folder: {e}")
            return f"Error creating folder in Google Drive: {str(e)}"
    
    def _create_folder_structure(self, folder_path: str) -> str:
        """Create nested folder structure and return the final folder ID."""
        # This is a simplified implementation
        # In practice, you'd need to handle the full folder creation logic
        parts = folder_path.strip('/').split('/')
        parent_id = 'root'
        
        for part in parts:
            if part:
                # Check if folder exists
                existing = self.drive_service.files().list(
                    q=f"name='{part}' and '{parent_id}' in parents and mimeType='application/vnd.google-apps.folder'",
                    fields="files(id, name)"
                ).execute()
                
                if existing.get('files'):
                    parent_id = existing['files'][0]['id']
                else:
                    # Create new folder
                    folder_metadata = {
                        'name': part,
                        'mimeType': 'application/vnd.google-apps.folder',
                        'parents': [parent_id]
                    }
                    folder = self.drive_service.files().create(body=folder_metadata).execute()
                    parent_id = folder.get('id')
        
        return parent_id

    def _find_or_create_folder(self, folder_name: str, parent_id: str = 'root') -> str:
        """Find existing folder or create new one."""
        try:
            # Search for existing folder
            existing = self.drive_service.files().list(
                q=f"name='{folder_name}' and '{parent_id}' in parents and mimeType='application/vnd.google-apps.folder'",
                fields="files(id, name)"
            ).execute()

            if existing.get('files'):
                return existing['files'][0]['id']

            # Create new folder
            folder_metadata = {
                'name': folder_name,
                'mimeType': 'application/vnd.google-apps.folder',
                'parents': [parent_id]
            }
            folder = self.drive_service.files().create(body=folder_metadata).execute()
            logger.info(f"Created new folder: {folder_name}")
            return folder.get('id')

        except Exception as e:
            logger.error(f"Error finding/creating folder {folder_name}: {e}")
            return parent_id  # Fallback to parent
    
    def _check_processed_status(self, query: str) -> str:
        """Check if a file has been processed."""
        try:
            # Extract file ID
            file_id = None
            if "file_id:" in query:
                file_id = query.split("file_id:")[1].strip().split()[0]
            elif "id:" in query:
                file_id = query.split("id:")[1].strip().split()[0]
            
            if not file_id:
                return "Please provide a file ID to check processing status."
            
            # Check for processing markers (this would integrate with your database)
            # For now, return a placeholder response
            result = {
                "status": "success",
                "file_id": file_id,
                "processed": False,
                "message": "File processing status check - integrate with database for actual status"
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error checking processed status: {e}")
            return f"Error checking file status: {str(e)}"
