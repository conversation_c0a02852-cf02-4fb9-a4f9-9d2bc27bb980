"""Main FastAPI application for Meeting Intelligence Agent."""

import os
import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse

# Import routers
from src.api.routers.agent import router as agent_router

# Import app constants
from src.constants.app import (
    APP_NAME, APP_DESCRIPTION, APP_VERSION,
    API_DOCS_URL, API_REDOC_URL, CORS_ORIGINS, CORS_METHODS, CORS_HEADERS, CORS_CREDENTIALS
)

logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title=APP_NAME,
    description=APP_DESCRIPTION,
    version=APP_VERSION,
    docs_url=API_DOCS_URL,
    redoc_url=API_REDOC_URL
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=CORS_CREDENTIALS,
    allow_methods=CORS_METHODS,
    allow_headers=CORS_HEADERS,
)

# Include routers
app.include_router(agent_router, prefix="/agent", tags=["agent"])


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "name": "Post Meeting Intelligence Agent",
        "version": "4.0.0",
        "description": "LangChain-powered Post meeting intelligence workflow with focused processing",
        "technology_stack": {
            "framework": "FastAPI",
            "ai_orchestration": "LangChain",
            "database": "MySQL 8.0",
            "ai_provider": "Google Cloud Vertex AI",
            "notifications": "Email"
        },
        "workflow_steps": [
            "1. Identify Meeting & Transcript",
            "2. Summarize Transcript (AI)",
            "3. Generate JSON & HTML Summaries",
            "4. Email Summaries to Attendees", 
            "5. Store Summaries in Google Drive"
        ],
        "features": [
            "LangChain orchestrated Post meetingworkflow",
            "AI-powered meeting transcript summarization",
            "Professional HTML/JSON output generation",
            "Email notifications to attendees",
            "Google Drive storage integration",
            "Smart duplicate prevention",
            "30-minute scheduled execution"
        ],
        "agent_capabilities": {
            "workflow_orchestration": True,
            "ai_summarization": True,
            "email_notifications": True,
            "drive_integration": True,
            "scheduled_execution": True
        },
        "available_tools": [
            "calendar_tool - Google Calendar integration",
            "drive_tool - Google Drive operations",
            "summarizer_tool - AI-powered summarization",
            "notification_tool - Email notifications",
            "file_manager_tool - File operations"
        ],
        "endpoints": {
            "docs": "/docs",
            "health": "/health",
            "workflow_trigger": "/agent/trigger-workflow",
            "workflow_status": "/agent/workflow-status",
            "scheduler_start": "/agent/start-scheduler",
            "scheduler_stop": "/agent/stop-scheduler",
            "scheduler_status": "/agent/scheduler-status",
            "health_check": "/agent/health"
        },
        "scheduling": {
            "available": True,
            "interval": "30 minutes",
            "features": [
                "Automatic calendar checking",
                "Smart duplicate detection",
                "Business hours configuration",
                "Manual force execution",
                "Performance tracking"
            ]
        },
        "timestamp": datetime.now().isoformat()
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Basic health check
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0",
            "services": {
                "api": "healthy",
                "langchain_agent": "available"
            }
        }
        
        # Try to import and check LangChain agent
        try:
            from src.agents.langchain_meeting_agent import get_langchain_agent
            agent = get_langchain_agent()
            health_status["services"]["langchain_agent"] = "healthy"
            health_status["agent_info"] = {
                "agent_type": "5_step_workflow",
                "tools_available": len(agent.tools),
                "workflow_capabilities": True,
                "ai_summarization": True
            }
        except Exception as e:
            health_status["services"]["langchain_agent"] = f"error: {str(e)}"
            health_status["status"] = "degraded"

        # Using LangChain agent for Post meetingworkflow
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )


@app.on_event("startup")
async def startup_event():
    """Startup event handler."""
    logger.info("Post Meeting Intelligence Agent API starting up...")
    logger.info("Technology Stack: FastAPI + LangChain + Vertex AI + MySQL + Email")


@app.on_event("shutdown")
async def shutdown_event():
    """Shutdown event handler."""
    logger.info("Post meeting Intelligence Agent API shutting down...")


# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    """Handle 404 errors."""
    return JSONResponse(
        status_code=404,
        content={
            "error": "Not Found",
            "message": "The requested resource was not found",
            "path": str(request.url.path),
            "timestamp": datetime.now().isoformat()
        }
    )


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    """Handle 500 errors."""
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "An internal server error occurred",
            "timestamp": datetime.now().isoformat()
        }
    )
