"""API models for Meeting Intelligence Agent."""

from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field

# Import app response formats and status constants
from src.constants.app import STANDARD_RESPONSE_FORMAT, ERROR_RESPONSE_FORMAT, RESPONSE_STATUS


# Request models
class TriggerRequest(BaseModel):
    """Request model for manual trigger."""
    config_override: Optional[Dict[str, Any]] = Field(None, description="Configuration overrides")


class FeedbackRequest(BaseModel):
    """Request model for feedback submission."""
    feedback_text: str = Field(..., description="Feedback text")
    rating: Optional[int] = Field(None, description="Rating (1-5)")
    category: Optional[str] = Field(None, description="Feedback category")


class EmailRequest(BaseModel):
    """Request model for email sending."""
    to_email: str = Field(..., description="Recipient email")
    subject: str = Field(..., description="Email subject")
    body: str = Field(..., description="Email body")
    template_name: Optional[str] = Field("default", description="Email template")


# Response models
class TriggerResponse(BaseModel):
    """Response model for manual trigger."""
    status: str
    message: str
    task_id: Optional[str] = None
    timestamp: str


class FeedbackResponse(BaseModel):
    """Response model for feedback submission."""
    status: str
    message: str
    feedback_id: Optional[str] = None
    timestamp: str


class FeedbackStatsResponse(BaseModel):
    """Response model for feedback statistics."""
    total_feedback: int
    average_rating: Optional[float] = None
    feedback_by_category: Dict[str, int]
    recent_feedback: List[Dict[str, Any]]


class EmailResponse(BaseModel):
    """Response model for email sending."""
    status: str
    message: str
    email_id: Optional[str] = None
    timestamp: str


class SummaryResponse(BaseModel):
    """Response model for summaries."""
    status: str
    summaries: List[Dict[str, Any]]
    count: int
    timestamp: str


class StatusResponse(BaseModel):
    """Response model for status check."""
    status: str
    services: Dict[str, str]
    timestamp: str
    uptime: Optional[str] = None


class ConfigResponse(BaseModel):
    """Response model for configuration."""
    status: str
    config: Dict[str, Any]
    timestamp: str


class ErrorResponse(BaseModel):
    """Response model for errors."""
    status: str = "error"
    error: str
    details: Optional[Dict[str, Any]] = None
    timestamp: str


class HealthResponse(BaseModel):
    """Response model for health check."""
    status: str
    services: Dict[str, str]
    timestamp: str
    version: str
