#!/usr/bin/env python3
"""
Test script to verify all file and folder connections are working properly.
This script tests imports, dependencies, and integration between components.
"""

import sys
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def test_core_imports():
    """Test core authentication and service imports."""
    print("🔍 Testing Core Imports...")
    
    tests = [
        ("GoogleAuthenticator", "src.services.utility.google_auth", "GoogleAuthenticator"),
        ("GoogleCalendarService", "src.services.utility.calendar_service", "GoogleCalendarService"),
        ("EmailService", "src.services.email_service", "EmailService"),
    ]
    
    results = []
    for name, module, class_name in tests:
        try:
            mod = __import__(module, fromlist=[class_name])
            cls = getattr(mod, class_name)
            results.append((name, True, None))
            print(f"  ✅ {name}")
        except Exception as e:
            results.append((name, False, str(e)))
            print(f"  ❌ {name}: {e}")
    
    return results


def test_tool_imports():
    """Test LangChain tool imports."""
    print("\n🔧 Testing Tool Imports...")
    
    tools = [
        ("CalendarTool", "src.tools.langchain_calendar_tool", "CalendarTool"),
        ("DriveTool", "src.tools.langchain_drive_tool", "DriveTool"),
        ("CalendarAttachmentTool", "src.tools.langchain_calendar_attachment_tool", "CalendarAttachmentTool"),
        ("SummarizerTool", "src.tools.langchain_summarizer_tool", "SummarizerTool"),
        ("NotificationTool", "src.tools.langchain_notification_tool", "NotificationTool"),
        ("FileManagerTool", "src.tools.langchain_file_manager_tool", "FileManagerTool"),
    ]
    
    results = []
    for name, module, class_name in tools:
        try:
            mod = __import__(module, fromlist=[class_name])
            cls = getattr(mod, class_name)
            results.append((name, True, None))
            print(f"  ✅ {name}")
        except Exception as e:
            results.append((name, False, str(e)))
            print(f"  ❌ {name}: {e}")
    
    return results


def test_agent_imports():
    """Test agent imports."""
    print("\n🤖 Testing Agent Imports...")
    
    agents = [
        ("LangChainMeetingAgent", "src.agents.langchain_meeting_agent", "LangChainMeetingAgent"),
    ]
    
    results = []
    for name, module, class_name in agents:
        try:
            mod = __import__(module, fromlist=[class_name])
            cls = getattr(mod, class_name)
            results.append((name, True, None))
            print(f"  ✅ {name}")
        except Exception as e:
            results.append((name, False, str(e)))
            print(f"  ❌ {name}: {e}")
    
    return results


def test_integration():
    """Test integration between components."""
    print("\n🔗 Testing Component Integration...")
    
    try:
        # Test GoogleAuthenticator initialization
        from src.services.utility.google_auth import GoogleAuthenticator
        auth = GoogleAuthenticator(
            credentials_path='./keys/gmail-credentials.json',  # Using existing file
            token_path='./keys/google-token.json'
        )
        print("  ✅ GoogleAuthenticator initialization")
        
        # Test EmailService with GoogleAuthenticator
        from src.services.email_service import EmailService
        email_service = EmailService(provider="gmail", google_auth=auth)
        print("  ✅ EmailService integration with GoogleAuthenticator")
        
        # Test CalendarTool with GoogleAuthenticator
        from src.tools.langchain_calendar_tool import CalendarTool
        calendar_tool = CalendarTool(auth=auth)
        print("  ✅ CalendarTool integration with GoogleAuthenticator")
        
        # Test DriveTool with GoogleAuthenticator
        from src.tools.langchain_drive_tool import DriveTool
        drive_tool = DriveTool(auth=auth)
        print("  ✅ DriveTool integration with GoogleAuthenticator")
        
        # Test LangChainMeetingAgent initialization
        from src.agents.langchain_meeting_agent import LangChainMeetingAgent
        agent = LangChainMeetingAgent()
        print("  ✅ LangChainMeetingAgent initialization")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Integration test failed: {e}")
        return False


def test_file_structure():
    """Test required file structure."""
    print("\n📁 Testing File Structure...")
    
    required_files = [
        "src/services/utility/google_auth.py",
        "src/services/utility/calendar_service.py",
        "src/services/email_service.py",
        "src/tools/langchain_calendar_tool.py",
        "src/tools/langchain_drive_tool.py",
        "src/tools/langchain_calendar_attachment_tool.py",
        "src/agents/langchain_meeting_agent.py",
        "test_oauth.py",
        "migrate_to_oauth.py",
    ]
    
    missing_files = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    return len(missing_files) == 0


def main():
    """Run all connection tests."""
    print("🧪 Testing File and Folder Connections")
    print("=" * 50)
    
    # Run all tests
    core_results = test_core_imports()
    tool_results = test_tool_imports()
    agent_results = test_agent_imports()
    integration_success = test_integration()
    file_structure_ok = test_file_structure()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 30)
    
    total_tests = len(core_results) + len(tool_results) + len(agent_results)
    passed_tests = sum(1 for _, success, _ in core_results + tool_results + agent_results if success)
    
    print(f"Import Tests: {passed_tests}/{total_tests} passed")
    print(f"Integration Test: {'✅ PASSED' if integration_success else '❌ FAILED'}")
    print(f"File Structure: {'✅ COMPLETE' if file_structure_ok else '❌ INCOMPLETE'}")
    
    # Overall result
    all_passed = (passed_tests == total_tests and integration_success and file_structure_ok)
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL CONNECTIONS ARE WORKING PROPERLY!")
        print("The OAuth2 implementation is fully integrated and ready to use.")
    else:
        print("⚠️  SOME ISSUES FOUND")
        print("Please review the failed tests above.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
