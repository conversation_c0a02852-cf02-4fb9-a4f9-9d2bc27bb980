"""LangChain Calendar Tool for autonomous meeting intelligence."""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Type
from pydantic import BaseModel, Field

from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun

from src.services.utility.google_auth import GoogleAuthenticator
from src.services.utility.calendar_service import GoogleCalendarService

# Import tool configs and categories
from src.constants.app import AVAILABLE_TOOLS, TOOL_CATEGORIES

logger = logging.getLogger(__name__)


class CalendarSearchInput(BaseModel):
    """Input for calendar search operations."""
    hours_back: int = Field(default=1, description="How many hours back to search for events")
    max_results: int = Field(default=50, description="Maximum number of events to return")
    calendar_id: str = Field(default="primary", description="Calendar ID to search")


class CalendarEventInput(BaseModel):
    """Input for getting specific calendar event details."""
    event_id: str = Field(description="The ID of the calendar event to retrieve")
    calendar_id: str = Field(default="primary", description="Calendar ID containing the event")


class CalendarTool(BaseTool):
    """
    LangChain tool for Google Calendar operations.

    This tool allows the agent to:
    - Search for recent calendar events
    - Get detailed event information
    - Find meeting attendees and metadata
    - Identify events that need transcript processing
    """

    name: str = "calendar_tool"
    description: str = AVAILABLE_TOOLS["calendar_tool"]["description"]
    category: str = AVAILABLE_TOOLS["calendar_tool"]["category"]

    # Declare auth as a class variable to avoid Pydantic validation issues
    auth: Optional[GoogleAuthenticator] = None
    calendar_service: Optional[GoogleCalendarService] = None

    def __init__(self, auth: GoogleAuthenticator, **kwargs):
        super().__init__(**kwargs)
        self.auth = auth
        self.calendar_service = GoogleCalendarService(auth)

    def get_event(self, event_id: str):
        """Get a single event by ID from recent events (last 24 hours)."""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        events = self.calendar_service.get_events_in_range(start_time=start_time, end_time=end_time)
        for event in events:
            if event.id == event_id:
                return event
        return None
    
    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute calendar operations synchronously."""
        try:
            # Parse the query to determine the operation
            if "recent events" in query.lower() or "last hour" in query.lower():
                return self._search_recent_events(query)
            elif "event details" in query.lower() or "event id" in query.lower():
                return self._get_event_details(query)
            elif "search" in query.lower():
                return self._search_events(query)
            else:
                return self._search_recent_events(query)
                
        except Exception as e:
            logger.error(f"Calendar tool error: {e}")
            return f"Error accessing calendar: {str(e)}"
    
    async def _arun(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Execute calendar operations asynchronously."""
        return self._run(query, run_manager)
    
    def _search_recent_events(self, query: str) -> str:
        """Search for recent calendar events."""
        try:
            # Extract hours back from query if specified
            hours_back = 1
            if "last" in query and "hour" in query:
                try:
                    # Try to extract number of hours
                    words = query.split()
                    for i, word in enumerate(words):
                        if word.isdigit() and i + 1 < len(words) and "hour" in words[i + 1]:
                            hours_back = int(word)
                            break
                except:
                    hours_back = 1
            
            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours_back)
            
            # Get events from calendar service
            events = self.calendar_service.get_events_in_range(
                start_time=start_time,
                end_time=end_time
            )
            
            if not events:
                return f"No calendar events found in the last {hours_back} hour(s)."
            
            # Format events for the agent
            event_summaries = []
            for event in events:
                summary = {
                    "id": event.id,
                    "title": event.summary or "No title",
                    "start_time": event.start_time.isoformat() if event.start_time else "",
                    "end_time": event.end_time.isoformat() if event.end_time else "",
                    "attendees": event.attendees if event.attendees else [],
                    "description": (event.description[:200] + "...") if event.description else "",
                    "location": event.location or "",
                    "organizer": event.organizer or ""
                }
                event_summaries.append(summary)
            
            result = {
                "status": "success",
                "events_found": len(events),
                "time_range": f"{start_time.isoformat()} to {end_time.isoformat()}",
                "events": event_summaries
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error searching recent events: {e}")
            return f"Error searching calendar events: {str(e)}"
    
    def _get_event_details(self, query: str) -> str:
        """Get detailed information about a specific event."""
        try:
            # Try to extract event ID from query
            event_id = None
            if "event_id:" in query:
                event_id = query.split("event_id:")[1].strip().split()[0]
            elif "id:" in query:
                event_id = query.split("id:")[1].strip().split()[0]
            
            if not event_id:
                return "Please provide an event ID to get event details. Format: 'event details for event_id: <id>'"
            
            # Get event details
            event = self.get_event(event_id)
            
            if not event:
                return f"Event with ID {event_id} not found."
            
            # Format detailed event information
            event_details = {
                "id": event.id,
                "title": event.summary or "No title",
                "description": event.description or "",
                "start_time": event.start_time.isoformat() if event.start_time else "",
                "end_time": event.end_time.isoformat() if event.end_time else "",
                "location": event.location or "",
                "organizer": event.organizer or "",
                "attendees": event.attendees if event.attendees else [],
                "created": "",
                "updated": "",
                "status": "",
                "html_link": "",
                "meeting_link": "",
                "attachments": []
            }
            
            return json.dumps(event_details, indent=2)
            
        except Exception as e:
            logger.error(f"Error getting event details: {e}")
            return f"Error retrieving event details: {str(e)}"
    
    def _search_events(self, query: str) -> str:
        """Search for events based on query criteria."""
        try:
            # Extract search parameters from query
            search_text = ""
            hours_back = 24  # Default to last 24 hours

            # Enhanced time parsing
            query_lower = query.lower()
            if "30 minutes" in query_lower or "last 30 minutes" in query_lower:
                hours_back = 0.5
            elif "60 minutes" in query_lower or "last 60 minutes" in query_lower or "last hour" in query_lower:
                hours_back = 1
            elif "2 hours" in query_lower or "last 2 hours" in query_lower:
                hours_back = 2
            elif "6 hours" in query_lower or "last 6 hours" in query_lower:
                hours_back = 6
            elif "12 hours" in query_lower or "last 12 hours" in query_lower:
                hours_back = 12
            elif "24 hours" in query_lower or "last 24 hours" in query_lower or "last day" in query_lower:
                hours_back = 24
            elif "week" in query_lower or "7 days" in query_lower:
                hours_back = 168

            # Simple query parsing for search text
            if "search for" in query_lower:
                search_text = query_lower.split("search for")[1].strip()
            elif "find" in query_lower and "meetings" not in query_lower:
                search_text = query_lower.split("find")[1].strip()

            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours_back)

            logger.info(f"Searching for events from {start_time} to {end_time} (last {hours_back} hours)")

            # Get events
            events = self.calendar_service.get_events_in_range(
                start_time=start_time,
                end_time=end_time
            )

            logger.info(f"Found {len(events)} events in time range")

            # Filter events based on search text if provided
            if search_text:
                filtered_events = []
                for event in events:
                    title = event.summary.lower() if event.summary else ""
                    description = event.description.lower() if event.description else ""
                    if search_text in title or search_text in description:
                        filtered_events.append(event)
                events = filtered_events
                logger.info(f"Filtered to {len(events)} events matching search text")
            
            if not events:
                return f"No events found matching the search criteria."
            
            # Format results
            event_summaries = []
            for event in events:
                summary = {
                    "id": event.id,
                    "title": event.summary or "No title",
                    "start_time": event.start_time.isoformat() if event.start_time else "",
                    "attendees_count": len(event.attendees) if event.attendees else 0,
                    "has_description": bool(event.description),
                    "location": event.location or ""
                }
                event_summaries.append(summary)
            
            result = {
                "status": "success",
                "search_criteria": search_text or "recent events",
                "events_found": len(events),
                "events": event_summaries
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            logger.error(f"Error searching events: {e}")
            return f"Error searching calendar: {str(e)}"
