"""
Database Models for Meeting Intelligence Agent
Configured for Google Cloud SQL (MySQL 8.0)
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, ForeignKey, DateTime, JSON, 
    Enum, Index, Float, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

Base = declarative_base()

# Enums for better type safety
class MeetingStatus(enum.Enum):
    PENDING_PROCESSING = "pending_processing"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TranscriptSource(enum.Enum):
    GOOGLE_MEET = "google_meet"
    ZOOM = "zoom"
    TEAMS = "teams"
    MANUAL_UPLOAD = "manual_upload"
    OTHER = "other"

class AgentRunStatus(enum.Enum):
    STARTED = "started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class EmailStatus(enum.Enum):
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    BOUNCED = "bounced"

class NotificationStatus(enum.Enum):
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    DELIVERED = "delivered"

class FeedbackRating(enum.Enum):
    EXCELLENT = "excellent"
    GOOD = "good"
    AVERAGE = "average"
    POOR = "poor"
    TERRIBLE = "terrible"

# Main Tables
class Meeting(Base):
    """Meeting information and metadata."""
    __tablename__ = 'meetings'

    id = Column(Integer, primary_key=True, autoincrement=True)
    google_event_id = Column(String(255), unique=True, nullable=False, index=True)
    title = Column(String(500), nullable=False)
    description = Column(Text)
    start_time = Column(DateTime, nullable=False, index=True)
    end_time = Column(DateTime, nullable=False)
    organizer_email = Column(String(255), nullable=False, index=True)
    attendees = Column(JSON)  # Store as JSON array
    meeting_link = Column(String(1000))
    location = Column(String(500))
    status = Column(Enum(MeetingStatus), default=MeetingStatus.PENDING_PROCESSING, index=True)
    transcript_found = Column(Boolean, default=False)
    summary_generated = Column(Boolean, default=False)
    emails_sent = Column(Boolean, default=False)
    drive_uploaded = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    transcripts = relationship("Transcript", back_populates="meeting", cascade="all, delete-orphan")
    summaries = relationship("MeetingSummary", back_populates="meeting", cascade="all, delete-orphan")
    agent_runs = relationship("AgentRun", back_populates="meeting", cascade="all, delete-orphan")
    email_logs = relationship("EmailLog", back_populates="meeting", cascade="all, delete-orphan")
    feedback = relationship("Feedback", back_populates="meeting", cascade="all, delete-orphan")

    # Indexes
    __table_args__ = (
        Index('idx_meeting_time_status', 'start_time', 'status'),
        Index('idx_meeting_organizer_time', 'organizer_email', 'start_time'),
    )

class Transcript(Base):
    """Meeting transcript data and metadata."""
    __tablename__ = 'transcripts'

    id = Column(Integer, primary_key=True, autoincrement=True)
    meeting_id = Column(Integer, ForeignKey('meetings.id'), nullable=False, index=True)
    google_drive_file_id = Column(String(255), unique=True, index=True)
    file_name = Column(String(500), nullable=False)
    file_path = Column(String(1000))
    source = Column(Enum(TranscriptSource), default=TranscriptSource.GOOGLE_MEET)
    content = Column(Text)  # Full transcript content
    word_count = Column(Integer)
    duration_minutes = Column(Float)
    language = Column(String(10), default='en')
    confidence_score = Column(Float)  # AI confidence in transcript accuracy
    processed = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    meeting = relationship("Meeting", back_populates="transcripts")

class MeetingSummary(Base):
    """AI-generated meeting summaries."""
    __tablename__ = 'meeting_summaries'

    id = Column(Integer, primary_key=True, autoincrement=True)
    meeting_id = Column(Integer, ForeignKey('meetings.id'), nullable=False, index=True)
    summary_data = Column(JSON)  # Complete summary as JSON
    html_content = Column(Text)  # HTML formatted summary
    json_file_path = Column(String(1000))
    html_file_path = Column(String(1000))
    drive_json_file_id = Column(String(255))
    drive_html_file_id = Column(String(255))
    ai_model_used = Column(String(100))  # e.g., "gemini-pro", "gpt-4"
    processing_time_seconds = Column(Float)
    token_count = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    meeting = relationship("Meeting", back_populates="summaries")

class AgentRun(Base):
    """Agent execution logs and performance tracking."""
    __tablename__ = 'agent_runs'

    id = Column(Integer, primary_key=True, autoincrement=True)
    meeting_id = Column(Integer, ForeignKey('meetings.id'), nullable=True, index=True)
    run_type = Column(String(50), nullable=False)  # 'scheduled', 'manual', 'api'
    start_time = Column(DateTime, nullable=False, index=True)
    end_time = Column(DateTime)
    status = Column(Enum(AgentRunStatus), default=AgentRunStatus.STARTED, index=True)
    steps_completed = Column(Integer, default=0)
    total_steps = Column(Integer, default=5)
    error_details = Column(Text)
    performance_metrics = Column(JSON)  # Store timing, token usage, etc.
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    meeting = relationship("Meeting", back_populates="agent_runs")

class EmailLog(Base):
    """Email notification tracking."""
    __tablename__ = 'email_logs'

    id = Column(Integer, primary_key=True, autoincrement=True)
    meeting_id = Column(Integer, ForeignKey('meetings.id'), nullable=False, index=True)
    recipient_email = Column(String(255), nullable=False, index=True)
    subject = Column(String(500))
    email_type = Column(String(50))  # 'summary', 'notification', 'error'
    status = Column(Enum(EmailStatus), default=EmailStatus.PENDING, index=True)
    sent_at = Column(DateTime)
    error_message = Column(Text)
    email_provider = Column(String(50))  # 'gmail', 'sendgrid'
    message_id = Column(String(255))  # Provider's message ID
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    meeting = relationship("Meeting", back_populates="email_logs")

    # Indexes
    __table_args__ = (
        Index('idx_email_recipient_status', 'recipient_email', 'status'),
        Index('idx_email_meeting_type', 'meeting_id', 'email_type'),
    )

class Feedback(Base):
    """User feedback on meeting summaries."""
    __tablename__ = 'feedback'

    id = Column(Integer, primary_key=True, autoincrement=True)
    meeting_id = Column(Integer, ForeignKey('meetings.id'), nullable=False, index=True)
    user_email = Column(String(255), nullable=False, index=True)
    rating = Column(Enum(FeedbackRating), nullable=False)
    accuracy_score = Column(Integer)  # 1-10 scale
    completeness_score = Column(Integer)  # 1-10 scale
    usefulness_score = Column(Integer)  # 1-10 scale
    comments = Column(Text)
    suggestions = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)

    # Relationships
    meeting = relationship("Meeting", back_populates="feedback")

class SystemConfig(Base):
    """System configuration and settings."""
    __tablename__ = 'system_config'

    id = Column(Integer, primary_key=True, autoincrement=True)
    config_key = Column(String(100), unique=True, nullable=False, index=True)
    config_value = Column(Text)
    config_type = Column(String(50))  # 'string', 'integer', 'boolean', 'json'
    description = Column(String(500))
    is_sensitive = Column(Boolean, default=False)  # For passwords, API keys
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class NotificationLog(Base):
    """General notification tracking (Slack, SMS, etc.)."""
    __tablename__ = 'notification_logs'

    id = Column(Integer, primary_key=True, autoincrement=True)
    meeting_id = Column(Integer, ForeignKey('meetings.id'), nullable=True, index=True)
    notification_type = Column(String(50), nullable=False)  # 'slack', 'sms', 'webhook'
    recipient = Column(String(255), nullable=False)
    message = Column(Text)
    status = Column(Enum(NotificationStatus), default=NotificationStatus.PENDING, index=True)
    sent_at = Column(DateTime)
    error_message = Column(Text)
    provider_response = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)

class ProcessingQueue(Base):
    """Queue for processing meetings."""
    __tablename__ = 'processing_queue'

    id = Column(Integer, primary_key=True, autoincrement=True)
    meeting_id = Column(Integer, ForeignKey('meetings.id'), nullable=False, index=True)
    priority = Column(Integer, default=5)  # 1-10, higher = more priority
    scheduled_for = Column(DateTime, nullable=False, index=True)
    attempts = Column(Integer, default=0)
    max_attempts = Column(Integer, default=3)
    last_error = Column(Text)
    status = Column(String(50), default='pending', index=True)  # 'pending', 'processing', 'completed', 'failed'
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Indexes
    __table_args__ = (
        Index('idx_queue_status_priority', 'status', 'priority'),
    )
