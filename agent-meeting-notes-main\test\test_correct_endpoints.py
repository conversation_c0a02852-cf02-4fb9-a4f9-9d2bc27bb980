#!/usr/bin/env python3
"""
Test the correct API endpoints for the Meeting Intelligence Agent.
"""

import requests
import json

def test_endpoint(url, method="GET", data=None):
    """Test a single endpoint and show the result."""
    try:
        print(f"\n🔍 Testing: {method} {url}")
        
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   ✅ Success!")
                # Show first few keys of response
                if isinstance(result, dict):
                    for key, value in list(result.items())[:3]:
                        if isinstance(value, str) and len(value) > 50:
                            print(f"   {key}: {value[:50]}...")
                        else:
                            print(f"   {key}: {value}")
                return True
            except:
                print(f"   ✅ Success (non-JSON)")
                return True
        else:
            print(f"   ❌ Failed: {response.text[:100]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    """Test the correct API endpoints."""
    print("🧪 Meeting Intelligence Agent - Correct Endpoint Testing")
    print("=" * 60)
    
    base_url = "http://localhost:8002"
    
    # Test the working endpoints
    endpoints = [
        # Basic endpoints
        ("GET", f"{base_url}/", "Root API info"),
        ("GET", f"{base_url}/health", "Main health check"),
        
        # Agent endpoints (with correct /agent prefix)
        ("GET", f"{base_url}/agent/health", "Agent health check"),
        ("GET", f"{base_url}/agent/scheduler-status", "Scheduler status"),
        ("GET", f"{base_url}/agent/workflow-status", "Workflow status"),
        ("POST", f"{base_url}/agent/trigger-workflow", "Trigger workflow"),
        ("POST", f"{base_url}/agent/start-scheduler", "Start scheduler"),
    ]
    
    results = {}
    
    for method, url, description in endpoints:
        print(f"\n📋 {description}")
        success = test_endpoint(url, method)
        results[description] = success
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 CORRECT ENDPOINTS TEST RESULTS")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} endpoints working")
    
    # Show correct URLs for manual testing
    print(f"\n🔧 CORRECT URLS FOR TESTING:")
    print(f"   API Documentation: {base_url}/docs")
    print(f"   Health Check: {base_url}/health")
    print(f"   Agent Health: {base_url}/agent/health")
    print(f"   Workflow Status: {base_url}/agent/workflow-status")
    print(f"   Trigger Workflow: POST {base_url}/agent/trigger-workflow")
    print(f"   Start Scheduler: POST {base_url}/agent/start-scheduler")
    
    print(f"\n📝 CURL COMMANDS:")
    print(f"   curl {base_url}/health")
    print(f"   curl {base_url}/agent/workflow-status")
    print(f"   curl -X POST {base_url}/agent/trigger-workflow")
    
    return passed > 0

if __name__ == "__main__":
    success = main()
    print(f"\n{'🎉 API is working!' if success else '❌ API has issues'}")
