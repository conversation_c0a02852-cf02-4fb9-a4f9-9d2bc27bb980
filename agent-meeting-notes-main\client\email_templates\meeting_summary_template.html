<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meeting Summary - {{meeting_title}}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1a1a1a;
            margin-bottom: 8px;
            font-size: 28px;
            font-weight: 600;
        }
        .subtitle {
            color: #666;
            font-size: 14px;
            margin-bottom: 24px;
        }
        .metadata {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 32px;
            border: 1px solid #e9ecef;
        }
        .metadata p {
            margin: 4px 0;
            font-size: 14px;
        }
        .metadata strong {
            font-weight: 600;
        }
        h2 {
            color: #1a1a1a;
            font-size: 20px;
            font-weight: 600;
            margin: 32px 0 16px 0;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }
        .summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            margin-bottom: 24px;
        }
        .outcome {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
        }
        .outcome-title {
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
        }
        .outcome-meta {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }
        .context {
            font-style: italic;
            color: #666;
            font-size: 14px;
            border-left: 3px solid #e9ecef;
            padding-left: 12px;
            margin-top: 12px;
        }

        .owner-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 16px;
            overflow: hidden;
        }
        .owner-header {
            background: #f8f9fa;
            padding: 16px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .owner-name {
            font-weight: 600;
            color: #1a1a1a;
        }
        .task-count {
            font-size: 12px;
            color: #666;
        }
        .tasks-table {
            width: 100%;
            border-collapse: collapse;
        }
        .tasks-table th {
            background: #f8f9fa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e9ecef;
        }
        .tasks-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f1f3f4;
            font-size: 14px;
        }
        .priority {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .priority-high {
            background: #fee;
            color: #c53030;
        }
        .priority-medium {
            background: #fffbeb;
            color: #d69e2e;
        }
        .priority-low {
            background: #f0f9ff;
            color: #2b6cb0;
        }
        .question {
            background: #fffbeb;
            border: 1px solid #fed7aa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }
        .session {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }
        .no-items {
            color: #666;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>{{title}}</h1>
        <div class="subtitle">Debrief generated on {{date_processed}}</div>

        <div class="metadata">
            <p><strong>Present:</strong> {{#attendees}}{{.}}{{^last}}, {{/last}}{{/attendees}}</p>
            <p><strong>Absent:</strong> {{#absent}}{{.}}{{^last}}, {{/last}}{{/absent}}{{^absent}}None{{/absent}}</p>
        </div>
        <h2>Executive Summary</h2>
        <div class="summary">
            {{executive_summary}}
        </div>
        <h2>Key Outcomes</h2>
        {{#outcomes}}
        <div class="outcome">
            <div class="outcome-title">{{decision}}</div>
            <div class="outcome-meta">
                <strong>Owner:</strong> {{owner}} |
                <strong>Rationale:</strong> {{rationale}}
            </div>
            {{#context}}
            <div class="context"><strong>Context:</strong> {{context}}</div>
            {{/context}}
        </div>
        {{/outcomes}}
        {{^outcomes}}
        <div class="no-items">No outcomes recorded.</div>
        {{/outcomes}}

        <h2>Open Questions</h2>
        {{#open_questions}}
        <div class="question">
            <div style="font-weight: 600; margin-bottom: 8px;">{{question}}</div>
            <div style="font-size: 14px; color: #666;">
                <strong>Owner:</strong> {{owner}} |
                <strong>Status:</strong> {{status}}
            </div>
        </div>
        {{/open_questions}}
        {{^open_questions}}
        <div class="no-items">No open questions were left unresolved.</div>
        {{/open_questions}}

        <h2>Working Sessions Needed</h2>
        {{#working_sessions_needed}}
        <div class="session">
            <div style="font-weight: 600; margin-bottom: 8px;">{{topic}}</div>
            <div style="font-size: 14px; color: #666; margin-bottom: 4px;">
                <strong>Participants:</strong> {{participants}}
            </div>
            <div style="font-size: 14px; color: #666;">
                <strong>Goal:</strong> {{goal}}
            </div>
        </div>
        {{/working_sessions_needed}}
        {{^working_sessions_needed}}
        <div class="no-items">No working sessions needed.</div>
        {{/working_sessions_needed}}
    </div>
</body>
</html>
