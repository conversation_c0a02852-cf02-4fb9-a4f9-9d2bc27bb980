"""Google OAuth2 authentication utilities for Gmail, Calendar and Drive APIs."""

import os
import logging
from typing import Optional
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

logger = logging.getLogger(__name__)


class GoogleAuthenticator:
    """Handles unified OAuth2 authentication for Gmail, Calendar and Drive services."""

    # Required scopes for Gmail, Calendar and Drive access
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/calendar.readonly',
        'https://www.googleapis.com/auth/calendar.events',
        'https://www.googleapis.com/auth/drive.readonly',
        'https://www.googleapis.com/auth/drive.file'
    ]

    def __init__(self, credentials_path: Optional[str] = None, token_path: Optional[str] = None):
        """
        Initialize the OAuth2 authenticator.

        Args:
            credentials_path: Path to Google OAuth2 client credentials JSON file
            token_path: Path to store/load OAuth2 tokens
        """
        self.credentials_path = credentials_path or os.getenv('GOOGLE_OAUTH_CREDENTIALS_PATH', './keys/google-oauth-credentials.json')
        self.token_path = token_path or os.getenv('GOOGLE_TOKEN_PATH', './keys/google-token.json')
        self.credentials = None

        if not self.credentials_path:
            logger.warning("No Google OAuth credentials path provided")
        else:
            self._load_credentials()

    def _load_credentials(self) -> bool:
        """
        Load or create Google OAuth2 credentials.

        Returns:
            True if credentials loaded successfully, False otherwise
        """
        try:
            creds = None

            # Load existing token if available
            if os.path.exists(self.token_path):
                creds = Credentials.from_authorized_user_file(self.token_path, self.SCOPES)
                logger.info("Loaded existing OAuth2 token")

            # If no valid credentials, get new ones
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    logger.info("Refreshing expired OAuth2 token")
                    creds.refresh(Request())
                else:
                    if not os.path.exists(self.credentials_path):
                        logger.error(f"OAuth credentials file not found: {self.credentials_path}")
                        return False

                    logger.info("Starting OAuth2 flow for new token")
                    flow = InstalledAppFlow.from_client_secrets_file(self.credentials_path, self.SCOPES)
                    creds = flow.run_local_server(port=0)

                # Save credentials for next run
                with open(self.token_path, 'w') as token:
                    token.write(creds.to_json())
                logger.info(f"OAuth2 token saved to {self.token_path}")

            self.credentials = creds
            logger.info("Google OAuth2 credentials loaded successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to load Google OAuth2 credentials: {e}")
            return False

    def get_calendar_service(self):
        """
        Get authenticated Google Calendar service.

        Returns:
            Google Calendar service instance or None if authentication failed
        """
        if not self.credentials:
            logger.error("No valid OAuth2 credentials available for Calendar service")
            return None

        try:
            service = build('calendar', 'v3', credentials=self.credentials)
            logger.info("Calendar service created successfully with OAuth2")
            return service

        except Exception as e:
            logger.error(f"Failed to create Calendar service: {e}")
            return None

    def get_drive_service(self):
        """
        Get authenticated Google Drive service.

        Returns:
            Google Drive service instance or None if authentication failed
        """
        if not self.credentials:
            logger.error("No valid OAuth2 credentials available for Drive service")
            return None

        try:
            service = build('drive', 'v3', credentials=self.credentials)
            logger.info("Drive service created successfully with OAuth2")
            return service

        except Exception as e:
            logger.error(f"Failed to create Drive service: {e}")
            return None

    def get_gmail_service(self):
        """
        Get authenticated Gmail service.

        Returns:
            Gmail service instance or None if authentication failed
        """
        if not self.credentials:
            logger.error("No valid OAuth2 credentials available for Gmail service")
            return None

        try:
            service = build('gmail', 'v1', credentials=self.credentials)
            logger.info("Gmail service created successfully with OAuth2")
            return service

        except Exception as e:
            logger.error(f"Failed to create Gmail service: {e}")
            return None

    def test_authentication(self) -> bool:
        """
        Test Google API OAuth2 authentication for all services.

        Returns:
            True if authentication is working for all services, False otherwise
        """
        try:
            # Test Gmail API
            gmail_service = self.get_gmail_service()
            if gmail_service:
                gmail_service.users().getProfile(userId='me').execute()
                logger.info("Gmail API OAuth2 authentication test passed")
            else:
                logger.error("Gmail API OAuth2 authentication test failed")
                return False

            # Test Calendar API
            calendar_service = self.get_calendar_service()
            if calendar_service:
                calendar_service.calendarList().list().execute()
                logger.info("Calendar API OAuth2 authentication test passed")
            else:
                logger.error("Calendar API OAuth2 authentication test failed")
                return False

            # Test Drive API
            drive_service = self.get_drive_service()
            if drive_service:
                drive_service.about().get(fields="user").execute()
                logger.info("Drive API OAuth2 authentication test passed")
            else:
                logger.error("Drive API OAuth2 authentication test failed")
                return False

            logger.info("All Google services OAuth2 authentication tests passed")
            return True

        except HttpError as e:
            logger.error(f"Google API OAuth2 authentication test failed: {e}")
            return False
        except Exception as e:
            logger.error(f"OAuth2 authentication test error: {e}")
            return False

    def refresh_credentials(self) -> bool:
        """
        Refresh Google OAuth2 credentials.

        Returns:
            True if refresh successful, False otherwise
        """
        try:
            if self.credentials and self.credentials.expired and self.credentials.refresh_token:
                self.credentials.refresh(Request())
                # Save refreshed token
                with open(self.token_path, 'w') as token:
                    token.write(self.credentials.to_json())
                logger.info("OAuth2 credentials refreshed and saved successfully")
                return True
            elif self.credentials and not self.credentials.expired:
                logger.info("OAuth2 credentials are still valid")
                return True
            else:
                logger.warning("OAuth2 credentials cannot be refreshed - re-authentication required")
                return False

        except Exception as e:
            logger.error(f"Failed to refresh OAuth2 credentials: {e}")
            return False

    def revoke_credentials(self) -> bool:
        """
        Revoke OAuth2 credentials and remove token file.

        Returns:
            True if revocation successful, False otherwise
        """
        try:
            if self.credentials:
                # Revoke the token
                revoke_url = f'https://oauth2.googleapis.com/revoke?token={self.credentials.token}'
                import requests
                response = requests.post(revoke_url)

                if response.status_code == 200:
                    logger.info("OAuth2 token revoked successfully")
                else:
                    logger.warning(f"Token revocation returned status: {response.status_code}")

            # Remove token file
            if os.path.exists(self.token_path):
                os.remove(self.token_path)
                logger.info(f"Token file removed: {self.token_path}")

            self.credentials = None
            return True

        except Exception as e:
            logger.error(f"Failed to revoke OAuth2 credentials: {e}")
            return False
