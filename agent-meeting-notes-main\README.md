# Meeting Intelligence Agent

[![Python](https://img.shields.io/badge/Python-3.11%2B-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104.0-green.svg)](https://fastapi.tiangolo.com)
[![<PERSON><PERSON>hain](https://img.shields.io/badge/LangChain-0.2.0-orange.svg)](https://langchain.com)
[![Google Cloud](https://img.shields.io/badge/Google%20Cloud-Vertex%20AI-red.svg)](https://cloud.google.com/vertex-ai)

An enterprise-grade AI system designed for end-to-end meeting intelligence. Built with FastAPI, LangChain, and Google Cloud Vertex AI, it delivers fully automated processing, summarization, distribution, and archiving of meeting content.

## Overview

The Meeting Intelligence Agent executes a comprehensive post-meeting workflow:

1. Identify Meeting & Transcript
2. Summarize Transcript via AI
3. Generate JSON & HTML Summaries
4. Email Summaries to Attendees
5. Store Summaries in Google Drive
6. Attach Summary to Calendar Event

## Key Features

* Complete six-step post-meeting pipeline
* Intelligent LangChain orchestration
* Google Cloud Vertex AI (Gemini Pro) summarization
* HTML-formatted summary emails
* Automated Drive file management and structuring
* Calendar event integration
* Configurable 30-minute recurring execution
* Prevents duplicate processing of meetings
* Exposes FastAPI endpoints for monitoring and control
* Interactive LangChain-based agent chat interface

## System Architecture

### Technology Stack

* **Framework**: FastAPI 0.104.0
* **AI Orchestration**: LangChain 0.2.0 + ChatVertexAI
* **AI Provider**: Google Cloud Vertex AI (Gemini 2.0 Flash)
* **Database**: MySQL 8.0
* **Authentication**: Google OAuth 2.0
* **Notifications**: Gmail API or SendGrid
* **Scheduling**: Python-Schedule and Cron

### Project Structure

```
src/
├── api/                       # FastAPI REST API
│   ├── main.py
│   └── routers/
├── agents/
│   └── langchain_meeting_agent.py
├── tools/                    # LangChain tools
├── services/                 # Business logic
├── utility/                  # Shared helpers
├── configuration/
├── constants/
```

## Getting Started

### Prerequisites

* Python 3.11+
* Google Cloud Project with Vertex AI
* Google Workspace account

### Installation

```bash
git clone <repository-url>
cd meeting-intelligence-agent
python -m venv venvagent
source venvagent/bin/activate  # On Windows: venvagent\Scripts\activate
pip install -r requirements.txt
mkdir -p keys meeting_summaries logging
```

### Environment Variables (.env)

```bash
GOOGLE_PROJECT_ID=your-google-cloud-project-id
GOOGLE_APPLICATION_CREDENTIALS=./keys/google-service-account.json
VERTEX_AI_LOCATION=us-central1
GMAIL_CREDENTIALS_PATH=./keys/gmail-credentials.json
DB_HOST=your-mysql-host
DB_PORT=3306
DB_NAME=meeting_intelligence
DB_USER=your-db-user
DB_PASSWORD=your-db-password
EMAIL_PROVIDER=gmail
```

## Running the System

### Manual Workflow

```bash
python run_agent.py
```

### API Server

```bash
python start_api.py
curl http://localhost:8000/docs
```

### Scheduler Execution

```bash
curl -X POST http://localhost:8000/agent/start-scheduler
curl http://localhost:8000/agent/scheduler-status
```

### Production (Cron)

```bash
*/30 * * * * cd /path/to/project && python -c "from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow; import asyncio; asyncio.run(run_autonomous_meeting_workflow())"
```

## API Endpoints

| Endpoint                  | Method | Description                         |
| ------------------------- | ------ | ----------------------------------- |
| `/`                       | GET    | API root                            |
| `/health`                 | GET    | Health check                        |
| `/agent/trigger-workflow` | POST   | Execute the full workflow           |
| `/agent/workflow-status`  | GET    | Check workflow capabilities         |
| `/agent/start-scheduler`  | POST   | Start 30-minute recurring workflow  |
| `/agent/stop-scheduler`   | POST   | Stop automated scheduler            |
| `/agent/scheduler-status` | GET    | Check if scheduler is active        |
| `/agent/chat`             | POST   | LangChain interactive command agent |

## LangChain Agent Chat Interface

Supports natural language instructions:

* "Execute the Post meeting workflow"
* "Summarize the transcript for product-testing"
* "Send the meeting summary via email"
* "Attach the summary to today's event"

Example:

```bash
curl -X POST http://localhost:8000/agent/chat \
-H "Content-Type: application/json" \
-d '{"message": "Execute the workflow for the last 30 minutes"}'
```

## Workflow Details

### Step 1: Identify Meetings and Transcripts

* Uses Google Calendar API to identify recent meetings
* Uses Google Drive API to fetch corresponding transcript files
* Applies fuzzy matching on time and title

### Step 2: Summarize Transcript

* Utilizes Gemini Pro on Vertex AI
* Outputs executive summary, action items, decisions

### Step 3: Generate JSON and HTML Summaries

* JSON: machine-readable format
* HTML: formatted for emails and UI display

### Step 4: Email Summaries

* Sends HTML summaries via Gmail API or SendGrid
* Supports multiple recipients and templating

### Step 5: Store Summaries in Drive

* Uploads both JSON and HTML
* Organized by year/month/project

### Step 6: Attach Summary to Calendar Event

* Attaches Google Drive file to relevant Calendar event

## Configuration

```bash
TIME_WINDOW_MINUTES=30
ENABLE_SCHEDULER=True
CLEANUP_TEMP_FILES=True
```

## File Organization

```
output/
├── html/
├── json/
└── Meeting_Summaries_HTML/
    └── 2025/
        └── 01/
            └── Project-X/
                ├── MeetingName.html
                └── MeetingName.json
```

## Troubleshooting

* AI dependency error: Install missing packages
* Auth error: Check service account credentials
* No transcript: Ensure calendar and Drive are shared with the service account
* Email fails: Validate Gmail API setup

## Security

* Grant Calendar/Drive access to the service account
* Store credentials securely in `./keys`

## Deployment

### Docker

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "start_api.py"]
```

### Systemd

```ini
[Unit]
Description=Meeting Intelligence Agent
After=network.target

[Service]
Type=simple
User=meetingagent
WorkingDirectory=/path/to/project
ExecStart=/usr/bin/python start_api.py
Restart=always

[Install]
WantedBy=multi-user.target
```

## Monitoring & Logs

* `/health` endpoint for status
* `/agent/scheduler-status` for automation checks
* Logs stored in `./logging/`

## Future Enhancements

* Multi-tenant support
* Advanced meeting analytics
* Integration with external platforms
* Configurable summary formats
* Batch meeting processing

## License

This project is licensed under the MIT License. See the `LICENSE` file for details.

## Acknowledgments

* LangChain for agent orchestration
* Google Cloud for AI and API services
* FastAPI for robust web framework

## Documentation

* `quick-start.md` – Initial setup instructions
* `api-documentation.md` – Endpoint descriptions
* `deployment-guide.md` – Production deployment support
* `CHANGELOG.md` – Version history and updates

---

